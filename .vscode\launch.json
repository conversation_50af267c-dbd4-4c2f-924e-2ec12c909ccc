{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "NestJS Debug (No Reload)", "runtimeExecutable": "node", "runtimeArgs": ["-r", "ts-node/register", "-r", "tsconfig-paths/register"], "args": ["${workspaceFolder}/src/main.ts"], "env": {"TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"}, "cwd": "${workspaceFolder}", "protocol": "inspector", "skipFiles": ["<node_internals>/**", "node_modules/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}