import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, Between } from 'typeorm';

import { Cronjob, JobType } from './entities/cronjob.entity';
import { Order, OrderStatus } from '../orders/entities/order.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { EmailService } from '../email/email.service';
import { OrderClientService } from '../orders/services/order-client.service';

@Injectable()
export class TaskSchedulerService {
  private readonly logger = new Logger(TaskSchedulerService.name);

  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Schedule)
    private readonly scheduleRepository: Repository<Schedule>,
    private readonly emailService: EmailService,
    private readonly orderClientService: OrderClientService,
  ) {}

  async executeJob(cronjob: Cronjob): Promise<any> {
    this.logger.log(`Executing job: ${cronjob.name} (${cronjob.jobType})`);

    switch (cronjob.jobType) {
      case JobType.EMAIL_REMINDER:
        return this.sendEmailReminders();
      
      case JobType.CLEANUP_LOGS:
        return this.cleanupOldLogs();
      
      case JobType.GENERATE_REPORTS:
        return this.generateReports();
      
      case JobType.SYNC_DATA:
        return this.syncExternalData();
      
      case JobType.BACKUP_DATABASE:
        return this.backupDatabase();
      
      case JobType.UPDATE_SCHEDULES:
        return this.updateSchedules();
      
      case JobType.SEND_NOTIFICATIONS:
        return this.sendNotifications();
      
      case JobType.CUSTOM:
        return this.executeCustomJob(cronjob);
      
      default:
        throw new Error(`Unknown job type: ${cronjob.jobType}`);
    }
  }

  private async sendEmailReminders(): Promise<any> {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // Find orders scheduled for tomorrow
    const ordersForTomorrow = await this.orderRepository.find({
      where: {
        status: OrderStatus.SCHEDULED,
        scheduledDate: tomorrowStr,
      },
      relations: ['property'],
    });

    const results = [];

    for (const order of ordersForTomorrow) {
      try {
        // Get client information using OrderClientService
        const emailVariables = await this.orderClientService.getEmailTemplateVariables(order.id);
        const clientEmails = await this.orderClientService.getOrderClientEmails(order.id);

        if (clientEmails.length > 0) {
          // Send reminder to all clients
          for (const email of clientEmails) {
            await this.emailService.sendTemplateEmail(
              'inspection-reminder',
              email,
              {
                ...emailVariables,
                orderNumber: order.orderNumber,
                propertyAddress: order.addressLine1 || 'N/A',
                inspectionDate: order.scheduledDate,
                inspectionTime: order.scheduledTime,
                inspectorName: 'Inspector Team',
              }
            );
          }
          results.push({ orderId: order.id, status: 'sent', emailsSent: clientEmails.length });
        } else {
          results.push({ orderId: order.id, status: 'skipped', reason: 'No client emails found' });
        }
      } catch (error) {
        this.logger.error(`Failed to send reminder for order ${order.id}:`, error.stack);
        results.push({ orderId: order.id, status: 'failed', error: error.message });
      }
    }

    return {
      jobType: 'email_reminder',
      processed: ordersForTomorrow.length,
      results,
    };
  }

  private async cleanupOldLogs(): Promise<any> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // This would typically clean up application logs
    // For now, we'll clean up old completed orders
    const oldCompletedOrders = await this.orderRepository.count({
      where: {
        status: OrderStatus.COMPLETED,
        completedAt: LessThan(thirtyDaysAgo),
      },
    });

    // In a real implementation, you might archive these instead of deleting
    this.logger.log(`Found ${oldCompletedOrders} old completed orders`);

    return {
      jobType: 'cleanup_logs',
      oldRecordsFound: oldCompletedOrders,
      message: 'Log cleanup completed',
    };
  }

  private async generateReports(): Promise<any> {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // Generate monthly statistics
    const monthlyOrders = await this.orderRepository.count({
      where: {
        createdAt: Between(startOfMonth, endOfMonth),
      },
    });

    const completedOrders = await this.orderRepository.count({
      where: {
        status: OrderStatus.COMPLETED,
        completedAt: Between(startOfMonth, endOfMonth),
      },
    });

    const report = {
      period: `${startOfMonth.toISOString().split('T')[0]} to ${endOfMonth.toISOString().split('T')[0]}`,
      totalOrders: monthlyOrders,
      completedOrders,
      completionRate: monthlyOrders > 0 ? (completedOrders / monthlyOrders * 100).toFixed(2) + '%' : '0%',
      generatedAt: new Date().toISOString(),
    };

    // In a real implementation, you might save this report to a file or database
    this.logger.log('Monthly report generated:', report);

    return {
      jobType: 'generate_reports',
      report,
    };
  }

  private async syncExternalData(): Promise<any> {
    // This would typically sync data with external APIs or services
    // For example, updating property values, inspector certifications, etc.
    
    this.logger.log('Syncing external data...');

    // Simulate external API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      jobType: 'sync_data',
      syncedRecords: 0,
      message: 'External data sync completed',
    };
  }

  private async backupDatabase(): Promise<any> {
    // This would typically create a database backup
    // For now, we'll just log the action
    
    this.logger.log('Creating database backup...');

    const backupInfo = {
      timestamp: new Date().toISOString(),
      tables: ['orders', 'schedules', 'inspectors', 'properties'],
      size: '0 MB', // Would be actual backup size
    };

    return {
      jobType: 'backup_database',
      backup: backupInfo,
      message: 'Database backup completed',
    };
  }

  private async updateSchedules(): Promise<any> {
    const today = new Date().toISOString().split('T')[0];

    // Mark past schedules as unavailable if they're still marked as available
    const pastSchedules = await this.scheduleRepository.update(
      {
        date: LessThan(today),
        available: true,
      },
      {
        available: false,
      }
    );

    return {
      jobType: 'update_schedules',
      updatedSchedules: pastSchedules.affected,
      message: 'Schedule updates completed',
    };
  }

  private async sendNotifications(): Promise<any> {
    // Send various system notifications
    // For example, low inspector availability alerts, overdue inspections, etc.

    const overdueOrders = await this.orderRepository.count({
      where: {
        status: OrderStatus.SCHEDULED,
        scheduledDate: LessThan(new Date().toISOString().split('T')[0]),
      },
    });

    if (overdueOrders > 0) {
      // Send alert to administrators
      this.logger.warn(`Found ${overdueOrders} overdue orders`);
    }

    return {
      jobType: 'send_notifications',
      overdueOrders,
      notificationsSent: overdueOrders > 0 ? 1 : 0,
    };
  }

  private async executeCustomJob(cronjob: Cronjob): Promise<any> {
    // Execute custom job based on configuration
    const config = cronjob.configuration || {};

    this.logger.log(`Executing custom job with config:`, config);

    // Custom job logic would go here
    // This could be dynamically loaded based on configuration

    return {
      jobType: 'custom',
      configuration: config,
      message: 'Custom job executed',
    };
  }
}
