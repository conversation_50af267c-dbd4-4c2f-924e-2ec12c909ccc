import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { InspectorsService } from './inspectors.service';
import { CreateInspectorDto } from './dto/create-inspector.dto';
import { UpdateInspectorDto } from './dto/update-inspector.dto';
import { InspectorQueryDto } from './dto/inspector-query.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';

@ApiTags('Inspectors')
@Controller('inspectors')
export class InspectorsController {
  constructor(private readonly inspectorsService: InspectorsService) {}

  @Post()
  @Auth('admin')
  @ApiOperation({ summary: 'Create a new inspector' })
  @ApiResponse({ status: 201, description: 'Inspector successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(
    @Body() createInspectorDto: CreateInspectorDto,
    @CurrentUser() user: any,
  ) {
    return this.inspectorsService.create(createInspectorDto);
  }

  @Get()
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get all inspectors with filtering' })
  @ApiResponse({ status: 200, description: 'Inspectors retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'isAvailable', required: false, type: Boolean })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(@Query() query: InspectorQueryDto) {
    return this.inspectorsService.findAll(query);
  }

  @Get('available')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get available inspectors for a specific date/time' })
  @ApiResponse({ status: 200, description: 'Available inspectors retrieved' })
  @ApiQuery({ name: 'date', required: true, type: String })
  @ApiQuery({ name: 'startTime', required: true, type: String })
  @ApiQuery({ name: 'endTime', required: true, type: String })
  async findAvailable(
    @Query('date') date: string,
    @Query('startTime') startTime: string,
    @Query('endTime') endTime: string,
  ) {
    return this.inspectorsService.findAvailable(date, startTime, endTime);
  }

  @Get(':id')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get inspector by ID' })
  @ApiResponse({ status: 200, description: 'Inspector retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Inspector not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.inspectorsService.findOne(id);
  }

  @Patch(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Update inspector' })
  @ApiResponse({ status: 200, description: 'Inspector updated successfully' })
  @ApiResponse({ status: 404, description: 'Inspector not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInspectorDto: UpdateInspectorDto,
  ) {
    return this.inspectorsService.update(id, updateInspectorDto);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete inspector' })
  @ApiResponse({ status: 200, description: 'Inspector deleted successfully' })
  @ApiResponse({ status: 404, description: 'Inspector not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.inspectorsService.remove(id);
  }

  @Get(':id/schedule')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get inspector schedule' })
  @ApiResponse({ status: 200, description: 'Inspector schedule retrieved' })
  async getSchedule(
    @Param('id', ParseIntPipe) id: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.inspectorsService.getSchedule(id, startDate, endDate);
  }

  @Get(':id/stats')
  @Auth('admin', 'inspector')
  @ApiOperation({ summary: 'Get inspector statistics' })
  @ApiResponse({ status: 200, description: 'Inspector statistics retrieved' })
  async getStats(@Param('id', ParseIntPipe) id: number) {
    return this.inspectorsService.getStats(id);
  }
}
