import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { OrderClientService } from './services/order-client.service';
import { Order } from './entities/order.entity';
import { User } from '../users/entities/user.entity';
import { Property } from '../properties/entities/property.entity';
import { Inspector } from '../inspectors/entities/inspector.entity';
import { Schedule } from '../schedules/entities/schedule.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Order, User, Property, Inspector, Schedule])],
  controllers: [OrdersController],
  providers: [OrdersService, OrderClientService],
  exports: [OrdersService, OrderClientService],
})
export class OrdersModule {}
