import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional, IsNotEmpty, Matches } from 'class-validator';

export class CheckConflictDto {
  @ApiProperty({
    description: 'Inspector ID',
    example: 1,
  })
  @IsNumber()
  inspectorId: number;

  @ApiProperty({
    description: 'Schedule date',
    example: '2024-01-15',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date must be in YYYY-MM-DD format' })
  date: string;

  @ApiProperty({
    description: 'Start time',
    example: '09:00',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Start time must be in HH:MM format' })
  startTime: string;

  @ApiProperty({
    description: 'End time',
    example: '17:00',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'End time must be in HH:MM format' })
  endTime: string;

  @ApiProperty({
    description: 'Schedule ID to exclude from conflict check',
    example: 123,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  excludeScheduleId?: number;
}
