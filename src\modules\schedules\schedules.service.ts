import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';

import { Schedule } from './entities/schedule.entity';
import { Inspector } from '../inspectors/entities/inspector.entity';
import { Order } from '../orders/entities/order.entity';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { ScheduleQueryDto } from './dto/schedule-query.dto';
import { CheckConflictDto } from './dto/check-conflict.dto';
import { BulkCreateScheduleDto } from './dto/bulk-create-schedule.dto';

@Injectable()
export class SchedulesService {
  constructor(
    @InjectRepository(Schedule)
    private readonly scheduleRepository: Repository<Schedule>,
    @InjectRepository(Inspector)
    private readonly inspectorRepository: Repository<Inspector>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  async create(createScheduleDto: CreateScheduleDto, user: any) {
    // Check if inspector exists
    const inspector = await this.inspectorRepository.findOne({
      where: { id: createScheduleDto.inspectorId },
    });

    if (!inspector) {
      throw new NotFoundException('Inspector not found');
    }

    // Check for conflicts
    const conflicts = await this.checkTimeConflicts(
      createScheduleDto.inspectorId,
      createScheduleDto.date,
      createScheduleDto.startTime,
      createScheduleDto.endTime,
    );

    if (conflicts.length > 0) {
      throw new ConflictException('Schedule conflicts with existing schedules');
    }

    // Check daily assignment limit (max 3 per day)
    const dailyCount = await this.getDailyAssignmentCount(
      createScheduleDto.inspectorId,
      createScheduleDto.date,
    );

    if (dailyCount >= 3) {
      throw new ConflictException('Inspector already has maximum assignments for this day');
    }

    const schedule = this.scheduleRepository.create(createScheduleDto);
    const savedSchedule = await this.scheduleRepository.save(schedule);

    return {
      schedule: await this.findOne(savedSchedule.id, user),
      message: 'Schedule created successfully',
    };
  }

  async bulkCreate(bulkCreateDto: BulkCreateScheduleDto, user: any) {
    const results = [];

    for (const scheduleData of bulkCreateDto.schedules) {
      try {
        const result = await this.create(scheduleData, user);
        results.push({ success: true, schedule: result.schedule });
      } catch (error) {
        results.push({ success: false, error: error.message, data: scheduleData });
      }
    }

    return {
      results,
      message: 'Bulk schedule creation completed',
    };
  }

  async findAll(query: ScheduleQueryDto, user: any) {
    const {
      page = 1,
      limit = 10,
      inspectorId,
      date,
      available,
      startDate,
      endDate,
      sortBy = 'date',
      sortOrder = 'ASC',
    } = query;

    const queryBuilder = this.scheduleRepository.createQueryBuilder('schedule');

    // Include relations
    queryBuilder.leftJoinAndSelect('schedule.inspector', 'inspector');
    queryBuilder.leftJoinAndSelect('schedule.order', 'order');

    // Apply filters
    if (inspectorId) {
      queryBuilder.andWhere('schedule.inspectorId = :inspectorId', { inspectorId });
    }

    if (date) {
      queryBuilder.andWhere('schedule.date = :date', { date });
    }

    if (startDate && endDate) {
      queryBuilder.andWhere('schedule.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    if (available !== undefined) {
      queryBuilder.andWhere('schedule.available = :available', { available });
    }

    // Apply user-based filtering
    if (user.role === 'inspector') {
      queryBuilder.andWhere('schedule.inspectorId = :userId', { userId: user.userId });
    }

    // Apply sorting
    queryBuilder.orderBy(`schedule.${sortBy}`, sortOrder);
    queryBuilder.addOrderBy('schedule.startTime', 'ASC');

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [schedules, total] = await queryBuilder.getManyAndCount();

    return {
      schedules,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number, user: any) {
    const schedule = await this.scheduleRepository.findOne({
      where: { id },
      relations: ['inspector', 'order'],
    });

    if (!schedule) {
      throw new NotFoundException('Schedule not found');
    }

    // Check permissions
    if (user.role === 'inspector' && schedule.inspectorId !== user.userId) {
      throw new ForbiddenException('You can only view your own schedules');
    }

    return schedule;
  }

  async update(id: number, updateScheduleDto: UpdateScheduleDto, user: any) {
    const schedule = await this.findOne(id, user);

    // Check permissions
    if (user.role === 'inspector' && schedule.inspectorId !== user.userId) {
      throw new ForbiddenException('You can only update your own schedules');
    }

    // Check for conflicts if time or date is being changed
    if (updateScheduleDto.date || updateScheduleDto.startTime || updateScheduleDto.endTime) {
      const conflicts = await this.checkTimeConflicts(
        updateScheduleDto.inspectorId || schedule.inspectorId,
        updateScheduleDto.date || schedule.date,
        updateScheduleDto.startTime || schedule.startTime,
        updateScheduleDto.endTime || schedule.endTime,
        id, // Exclude current schedule from conflict check
      );

      if (conflicts.length > 0) {
        throw new ConflictException('Schedule conflicts with existing schedules');
      }
    }

    await this.scheduleRepository.update(id, updateScheduleDto);

    const updatedSchedule = await this.findOne(id, user);
    return {
      schedule: updatedSchedule,
      message: 'Schedule updated successfully',
    };
  }

  async remove(id: number, user: any) {
    const schedule = await this.findOne(id, user);

    // Check permissions
    if (user.role === 'inspector' && schedule.inspectorId !== user.userId) {
      throw new ForbiddenException('You can only delete your own schedules');
    }

    // Check if schedule has an assigned order
    if (schedule.inspectionOrderId) {
      throw new BadRequestException('Cannot delete schedule with assigned order');
    }

    await this.scheduleRepository.remove(schedule);

    return {
      message: 'Schedule deleted successfully',
    };
  }

  async checkConflicts(checkConflictDto: CheckConflictDto) {
    const conflicts = await this.checkTimeConflicts(
      checkConflictDto.inspectorId,
      checkConflictDto.date,
      checkConflictDto.startTime,
      checkConflictDto.endTime,
      checkConflictDto.excludeScheduleId,
    );

    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
    };
  }

  async assignOrder(scheduleId: number, orderId: number, user: any) {
    const schedule = await this.findOne(scheduleId, user);
    const order = await this.orderRepository.findOne({ where: { id: orderId } });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    if (!schedule.available) {
      throw new BadRequestException('Schedule is not available for assignment');
    }

    schedule.inspectionOrderId = orderId;
    schedule.available = false;

    await this.scheduleRepository.save(schedule);

    return {
      message: 'Order assigned to schedule successfully',
      schedule: await this.findOne(scheduleId, user),
    };
  }

  async unassignOrder(scheduleId: number, user: any) {
    const schedule = await this.findOne(scheduleId, user);

    schedule.inspectionOrderId = null;
    schedule.available = true;

    await this.scheduleRepository.save(schedule);

    return {
      message: 'Order unassigned from schedule successfully',
      schedule: await this.findOne(scheduleId, user),
    };
  }

  async getInspectorAvailability(inspectorId: number, startDate: string, endDate: string) {
    const schedules = await this.scheduleRepository.find({
      where: {
        inspectorId,
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC', startTime: 'ASC' },
    });

    const availability = this.processAvailabilityData(schedules, startDate, endDate);

    return {
      inspectorId,
      startDate,
      endDate,
      availability,
    };
  }

  async getCalendarView(inspectorId: number, month?: string, year?: string) {
    const currentDate = new Date();
    const targetMonth = month ? parseInt(month) - 1 : currentDate.getMonth();
    const targetYear = year ? parseInt(year) : currentDate.getFullYear();

    const startDate = new Date(targetYear, targetMonth, 1).toISOString().split('T')[0];
    const endDate = new Date(targetYear, targetMonth + 1, 0).toISOString().split('T')[0];

    const schedules = await this.scheduleRepository.find({
      where: {
        inspectorId,
        date: Between(startDate, endDate),
      },
      relations: ['order'],
      order: { date: 'ASC', startTime: 'ASC' },
    });

    return {
      month: targetMonth + 1,
      year: targetYear,
      schedules: this.formatCalendarData(schedules),
    };
  }

  async getScheduleStats() {
    const totalSchedules = await this.scheduleRepository.count();
    const availableSchedules = await this.scheduleRepository.count({
      where: { available: true },
    });
    const assignedSchedules = await this.scheduleRepository.count({
      where: { available: false },
    });

    const schedulesByInspector = await this.scheduleRepository
      .createQueryBuilder('schedule')
      .leftJoin('schedule.inspector', 'inspector')
      .select('inspector.name', 'inspectorName')
      .addSelect('COUNT(*)', 'scheduleCount')
      .groupBy('inspector.id, inspector.name')
      .getRawMany();

    return {
      totalSchedules,
      availableSchedules,
      assignedSchedules,
      schedulesByInspector,
    };
  }

  async createRecurringSchedules(recurringData: any, user: any) {
    // Implementation for creating recurring schedules
    // This would generate multiple schedules based on recurrence pattern
    const { inspectorId, startDate, endDate, pattern, timeSlots } = recurringData;

    const schedules = [];
    // Logic to generate recurring schedules based on pattern
    // This is a simplified implementation

    return {
      schedules,
      message: 'Recurring schedules created successfully',
    };
  }

  private async checkTimeConflicts(
    inspectorId: number,
    date: string,
    startTime: string,
    endTime: string,
    excludeScheduleId?: number,
  ) {
    const queryBuilder = this.scheduleRepository.createQueryBuilder('schedule');

    queryBuilder.where('schedule.inspectorId = :inspectorId', { inspectorId });
    queryBuilder.andWhere('schedule.date = :date', { date });
    queryBuilder.andWhere(
      '(schedule.startTime < :endTime AND schedule.endTime > :startTime)',
      { startTime, endTime },
    );

    if (excludeScheduleId) {
      queryBuilder.andWhere('schedule.id != :excludeScheduleId', { excludeScheduleId });
    }

    return queryBuilder.getMany();
  }

  private async getDailyAssignmentCount(inspectorId: number, date: string) {
    return this.scheduleRepository.count({
      where: {
        inspectorId,
        date,
        available: false,
      },
    });
  }

  private processAvailabilityData(schedules: Schedule[], startDate: string, endDate: string) {
    // Process schedules to create availability data structure
    const availability = {};
    
    schedules.forEach(schedule => {
      if (!availability[schedule.date]) {
        availability[schedule.date] = [];
      }
      availability[schedule.date].push({
        id: schedule.id,
        startTime: schedule.startTime,
        endTime: schedule.endTime,
        available: schedule.available,
        hasOrder: !!schedule.inspectionOrderId,
      });
    });

    return availability;
  }

  private formatCalendarData(schedules: Schedule[]) {
    return schedules.map(schedule => ({
      id: schedule.id,
      date: schedule.date,
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      available: schedule.available,
      order: schedule.order ? {
        id: schedule.order.id,
        orderNumber: schedule.order.orderNumber,
        clientIds: schedule.order.clientIds,
      } : null,
    }));
  }
}
